
BEGIN;

CREATE TABLE IF NOT EXISTS "ams_HAUL_STATUSES" (
    "code" VARCHAR(20) NOT NULL,
    "label" VARCHAR(20) NOT NULL,
    "description" VARCHAR(50) NOT NULL,
    PRIMARY KEY ("code")
);

INSERT INTO "ams_HAUL_STATUSES" (code, label, description) VALUES
    ('P2H', 'P2H', 'Pit to Hauling'),
    ('READY', 'Ready', 'Ready for operation'),
    ('DELAY', 'Delay', 'Delayed operation'),
    ('IDLE', 'Idle', 'Idle state'),
    ('BREAKDOWN', 'Breakdown', 'Equipment breakdown')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ams_HAUL_SUB_STATUSES" (
    "code" VARCHAR(50) NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "description" VARCHAR(100) NOT NULL,
    "main_status_code" VARCHAR(20) NOT NULL,
    PRIMARY KEY ("code"),
    FOREIGN KEY ("main_status_code") REFERENCES "ams_HAUL_STATUSES"("code")
);

INSERT INTO "ams_HAUL_SUB_STATUSES" (code, label, description, main_status_code) VALUES
    ('P2H', 'P2H', 'Pit to Hauling', 'P2H'),
    ('TRAVELING_TO_LOADING', 'Traveling to Loading', 'Traveling to Loading', 'READY'),
    ('QUEUEING_FOR_LOADING', 'Antri Loading', 'Queue for Loading', 'READY'),
    ('LOADING', 'Loading', 'Loading Process', 'READY'),
    ('TRAVELING_TO_WB_LOAD', 'Traveling to WB (Muatan)', 'Traveling to Weight Bridge (Loaded)', 'READY'),
    ('QUEUEING_FOR_WB_LOAD', 'Antri WB (Muatan)', 'Queue at Weight Bridge (Loaded)', 'READY'),
    ('WEIGHING_THE_LOAD', 'Timbang Muatan', 'Weighing (Loaded)', 'READY'),
    ('TRAVELING_TO_DUMPING', 'Traveling to Dumping', 'Traveling to Dumping', 'READY'),
    ('QUEUEING_FOR_DUMPING', 'Antri Dumping', 'Queue for Dumping', 'READY'),
    ('DUMPING', 'Dumping', 'Dumping Process', 'READY'),
    ('TRAVELING_TO_WB_EMPTY', 'Traveling to WB (Kosong)', 'Traveling to Weight Bridge (Empty)', 'READY'),
    ('QUEUEING_FOR_WB_EMPTY', 'Antri WB (Kosong)', 'Queue at Weight Bridge (Empty)', 'READY'),
    ('WEIGHING_EMPTY', 'Timbang Kosong', 'Weighing (Empty)', 'READY'),
    ('SHIFT_CHANGE', 'Ganti Shift', 'Shift Change', 'DELAY'),
    ('REST_MEALS', 'Istirahat & Makan', 'Break & Meal', 'DELAY'),
    ('UNIT_CLEANING', 'Pembersihan Unit', 'Unit Cleaning', 'DELAY'),
    ('WORSHIP_SERVICE', 'Ibadah', 'Prayer Time', 'DELAY'),
    ('WEIGHBRIDGE_PROBLEMS', 'Weightbridge Bermasalah', 'Weight Bridge Problem', 'DELAY'),
    ('FATIGUE_TEST', 'Fatigue Test', 'Fatigue Test', 'DELAY'),
    ('ROAD_REPAIRS', 'Perbaikan Jalan', 'Road Repair', 'DELAY'),
    ('WEATHER_CONDITIONS', 'Kondisi Cuaca', 'Weather Condition', 'IDLE'),
    ('CUSTOMER_ORDER', 'Customer Order', 'Customer Order', 'IDLE'),
    ('PERIODIC_INSPECTION', 'Periodic Inspection', 'Periodic Inspection', 'BREAKDOWN'),
    ('SCHEDULE_MAINTENANCE', 'Schedule Maintenance', 'Schedule Maintenance', 'BREAKDOWN'),
    ('TIRE_MAINTENANCE', 'Tyre Maintenance', 'Tyre Maintenance', 'BREAKDOWN'),
    ('UNSCHEDULE_MAINTENANCE', 'Unschedule Maintenance', 'Unschedule Maintenance', 'BREAKDOWN')
ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ams_HAUL_SUB_STATUS_CYCLES" (
    prev_code VARCHAR(50) NOT NULL,
    next_code VARCHAR(50) NOT NULL,
    PRIMARY KEY (prev_code, next_code),
    FOREIGN KEY (prev_code) REFERENCES "ams_HAUL_SUB_STATUSES"("code"),
    FOREIGN KEY (next_code) REFERENCES "ams_HAUL_SUB_STATUSES"("code")
)

COMMIT;

